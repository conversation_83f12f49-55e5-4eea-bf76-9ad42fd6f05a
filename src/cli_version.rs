// CLI version module implementation

impl VCloudDB {
    /// Create a new CLI version from JSON string
    pub fn insert_cli_version(&mut self, cli_version_json: String) -> anyhow::Result<String> {
        let mut cli_version: CliVersion = serde_json::from_str(&cli_version_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI version JSON: {}", e))?;
        
        // Validate required fields
        if cli_version.version.is_empty() {
            return Err(anyhow::anyhow!("Version cannot be empty"));
        }

        // Use version as ID if _id is empty
        if cli_version._id.is_empty() {
            cli_version._id = cli_version.version.clone();
        }

        if self.cli_versions.contains(&cli_version._id) {
            return Err(anyhow::anyhow!("CLI version with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_timestamp_handling_cli_version(&mut cli_version);

        // Ensure deleted_at is 0 for new CLI versions
        cli_version.deleted_at = 0;

        self.cli_versions.insert(&cli_version._id, &cli_version);
        Ok(cli_version._id)
    }


    pub fn insert_many_cli_version(&mut self, cli_versions_json: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let cli_versions: Vec<CliVersion> = serde_json::from_str(&cli_versions_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI versions JSON: {}. Expected format: [{{\"version\": \"1.0.0\", \"changeLog\": \"Initial release\", ...}}, ...]", e))?;

        // Validate input is not empty
        if cli_versions.is_empty() {
            return Err(anyhow::anyhow!("CLI versions array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to prevent duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut cli_version in cli_versions {
            // Validate required fields
            if cli_version.version.is_empty() {
                result.errors.push(format!("CLI version has empty version field"));
                continue;
            }

            // Use version as ID if _id is empty
            if cli_version._id.is_empty() {
                cli_version._id = cli_version.version.clone();
            }

            // Check for duplicates within this batch
            if batch_ids.contains(&cli_version._id) {
                result.errors.push(format!("Duplicate CLI version ID '{}' within batch", cli_version._id));
                continue;
            }
            batch_ids.insert(cli_version._id.clone());

            // Check if CLI version already exists in storage
            if self.cli_versions.contains(&cli_version._id) {
                result.errors.push(format!("CLI version '{}' already exists", cli_version._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_timestamp_handling_cli_version(&mut cli_version);

            // Ensure deleted_at is 0 for new CLI versions
            cli_version.deleted_at = 0;

            // Insert the CLI version
            self.cli_versions.insert(&cli_version._id, &cli_version);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single CLI version by ID
    pub fn get_cli_version(&self, id: String) -> anyhow::Result<String> {
        let cli_version = self.cli_versions.get(&id);
        match cli_version {
            Some(cli_version) => Ok(serde_json::to_string(&cli_version)?),
            None => Err(anyhow::anyhow!("not found")),
        }
    }

    /// Update an existing CLI version from JSON string
    pub fn update_cli_version(&mut self, cli_version_json: String) -> anyhow::Result<()> {
        let mut cli_version: CliVersion = serde_json::from_str(&cli_version_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI version JSON: {}", e))?;

        // Validate required fields
        if cli_version.version.is_empty() {
            return Err(anyhow::anyhow!("Version cannot be empty"));
        }

        // Use version as ID if _id is empty
        if cli_version._id.is_empty() {
            cli_version._id = cli_version.version.clone();
        }

        if !self.cli_versions.contains(&cli_version._id) {
            return Err(anyhow::anyhow!("CLI version not found"));
        }

        // Apply timestamp handling logic for updates
        if cli_version.updated_at == 0 {
            cli_version.updated_at = self.get_current_timestamp();
        }

        self.cli_versions.insert(&cli_version._id, &cli_version);
        Ok(())
    }

    /// Apply timestamp handling logic for CLI versions
    fn apply_timestamp_handling_cli_version(&self, cli_version: &mut CliVersion) {
        let current_timestamp = self.get_current_timestamp();
        
        // Set created_at if it's 0 (new CLI version)
        if cli_version.created_at == 0 {
            cli_version.created_at = current_timestamp;
        }
        
        // Always update updated_at for new CLI versions
        if cli_version.updated_at == 0 {
            cli_version.updated_at = current_timestamp;
        }
    }

    // Internal implementation for batch updating CLI versions with partial updates
    pub(crate) fn update_many_cli_version(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let update_params: CliVersionUpdate = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionUpdate JSON: {}", e))?;

        let params = update_params.filter;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find CLI versions matching the filter criteria
        let mut cli_versions_to_update = Vec::new();

        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let cli_version = iter.value().map_err(|e| anyhow::anyhow!("Failed to get CLI version: {}", e))?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                cli_versions_to_update.push(cli_version);
            }
        }

        // Update each matching CLI version with partial data
        for mut cli_version in cli_versions_to_update {
            // Parse update data as a generic JSON object for partial updates
            if let Some(update_obj) = update_params.update_data.as_object() {
                // Update only specified fields
                for (key, value) in update_obj {
                    match key.as_str() {
                        "version" => {
                            if let Some(version) = value.as_str() {
                                cli_version.version = version.to_string();
                            }
                        }
                        "changeLog" => {
                            if let Some(change_log) = value.as_str() {
                                cli_version.change_log = change_log.to_string();
                            }
                        }
                        "minimalSupported" => {
                            if let Some(minimal_supported) = value.as_str() {
                                cli_version.minimal_supported = minimal_supported.to_string();
                            }
                        }
                        "deletedAt" => {
                            if let Some(deleted_at) = value.as_i64() {
                                cli_version.deleted_at = deleted_at;
                            }
                        }
                        // Add more fields as needed
                        _ => {} // Ignore unknown fields
                    }
                }
            }

            // Update timestamp
            cli_version.updated_at = self.get_current_timestamp();

            self.cli_versions.insert(&cli_version._id, &cli_version);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    // Internal implementation for bulk write operations on CLI versions
    pub(crate) fn bulk_write_cli_version(&mut self, bulk_write_json_string: String) -> anyhow::Result<String> {
        let operations: Vec<CliVersionBulkWriteOperation> = serde_json::from_str(&bulk_write_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionBulkWriteOperation JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        if let Ok(cli_versions_json) = serde_json::to_string(&data) {
                            match self.insert_many_cli_version(cli_versions_json) {
                                Ok(insert_result) => {
                                    if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&insert_result) {
                                        result.created += batch_result.created;
                                        result.errors.extend(batch_result.errors);
                                    }
                                }
                                Err(e) => {
                                    result.errors.push(format!("Insert operation failed: {}", e));
                                }
                            }
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        let update_params = CliVersionUpdate {
                            filter,
                            update_data: data,
                        };
                        let update_json = serde_json::to_string(&update_params)?;
                        match self.update_many_cli_version(update_json) {
                            Ok(update_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                    result.updated += batch_result.updated;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Update operation failed: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        let filter_json = serde_json::to_string(&filter)?;
                        match self.delete_many_cli_version(filter_json) {
                            Ok(delete_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                                    result.deleted += batch_result.deleted;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Delete operation failed: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single CLI version (HARD DELETE)
    pub(crate) fn delete_cli_version(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CliVersionQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionQueryParams JSON: {}", e))?;

        // Find the first matching CLI version
        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let cli_version = iter.value().map_err(|e| anyhow::anyhow!("Failed to get CLI version: {}", e))?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                // Hard delete: completely remove from storage
                self.cli_versions.remove(&cli_version._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting CLI versions
    pub(crate) fn delete_many_cli_version(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CliVersionQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find CLI versions matching the filter criteria
        let mut cli_versions_to_delete = Vec::new();

        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let cli_version = iter.value().map_err(|e| anyhow::anyhow!("Failed to get CLI version: {}", e))?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                cli_versions_to_delete.push(cli_version);
            }
        }

        // Delete each matching CLI version (HARD DELETE)
        for cli_version in cli_versions_to_delete {
            // Hard delete: completely remove from storage
            self.cli_versions.remove(&cli_version._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query CLI versions with comprehensive filtering, pagination, and sorting
    pub fn find_cli_version(&self, params_json: String) -> anyhow::Result<String> {
        let params: CliVersionQueryParams = serde_json::from_str(&params_json)?;

        let mut cli_versions = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use the most efficient index based on provided filters
        if let Some(ref version) = params.version {
            // Use version index with efficient sorting
            self.query_cli_version_with_index("cli_versions_version", version, &params, &mut cli_versions, &mut count, limit, offset)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.query_cli_version_with_created_at(&params, &mut cli_versions, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&cli_versions)?)
    }

    /// Count CLI versions with advanced filtering
    pub fn count_cli_version(&self, params_json: String) -> anyhow::Result<String> {
        let params: CliVersionQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        if let Some(ref version) = params.version {
            // Use version index for efficient counting
            self.count_cli_version_with_index("cli_versions_version", version, &params, &mut count)?;
        } else {
            // Use created_at index for general counting
            self.count_cli_version_with_created_at(&params, &mut count)?;
        }

        Ok(count.to_string())
    }

    /// Helper function to check if a CLI version matches the given filters
    pub(crate) fn matches_cli_version_filters(&self, cli_version: &CliVersion, params: &CliVersionQueryParams) -> bool {
        // Skip deleted CLI versions unless specifically querying for them
        if cli_version.deleted_at > 0 {
            return false;
        }

        // Check version filter
        if let Some(ref version) = params.version {
            if cli_version.version != *version {
                return false;
            }
        }

        // Check minimal_supported filter
        if let Some(ref minimal_supported) = params.minimal_supported {
            if cli_version.minimal_supported != *minimal_supported {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if cli_version.created_at < start {
                return false;
            }
        }

        if let Some(end) = params.created_at_end {
            if cli_version.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if cli_version.updated_at < start {
                return false;
            }
        }

        if let Some(end) = params.updated_at_end {
            if cli_version.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_cli_version_with_created_at(
        &self,
        params: &CliVersionQueryParams,
        cli_versions: &mut Vec<CliVersion>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if cli_versions.len() >= limit as usize {
                    break;
                }
                cli_versions.push(cli_version);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_cli_version_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &CliVersionQueryParams,
        cli_versions: &mut Vec<CliVersion>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) = (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.cli_versions.index(index_name).iter(sort_desc, &start_key, &end_key);
        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if cli_versions.len() >= limit as usize {
                    break;
                }
                cli_versions.push(cli_version);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_cli_version_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &CliVersionQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.cli_versions.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_cli_version_with_created_at(
        &self,
        params: &CliVersionQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}
