// Order module implementation

impl VCloudDB {
    /// Create a new order from JSON string
    pub fn insert_order(&mut self, order_json: String) -> anyhow::Result<String> {
        let mut order: Order = serde_json::from_str(&order_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

        // Validate required fields
        if order._id.is_empty() {
            return Err(anyhow::anyhow!("Order ID cannot be empty"));
        }

        if self.orders.contains(&order._id) {
            return Err(anyhow::anyhow!("Order with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_order_timestamp_handling(&mut order);

        // Ensure deleted_at is 0 for new orders
        order.deleted_at = 0;

        self.orders.insert(&order._id, &order);
        Ok(order._id)
    }

    /// Internal implementation for batch inserting orders
    pub(crate) fn insert_many_order(&mut self, insert_many_json_string: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let orders: Vec<Order> = serde_json::from_str(&insert_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse orders JSON: {}. Expected format: [{{\"ID\": \"order1\", \"Amount\": 100.0, ...}}, ...]", e))?;

        // Validate input is not empty
        if orders.is_empty() {
            return Err(anyhow::anyhow!("Orders array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut order in orders {
            // Validate required fields
            if order._id.is_empty() {
                result.errors.push("Order ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicate IDs within the batch
            if !batch_ids.insert(order._id.clone()) {
                result.errors.push(format!("Duplicate order ID '{}' found within batch", order._id));
                continue;
            }

            // Check for existing order with same ID
            if self.orders.contains(&order._id) {
                result.errors.push(format!("Order with ID '{}' already exists in database", order._id));
                continue;
            }

            // Validate business logic constraints
            if order.amount < 0.0 {
                result.errors.push(format!("Order '{}' has invalid negative amount: {}", order._id, order.amount));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_order_timestamp_handling(&mut order);

            // Ensure deleted_at is 0 for new orders
            order.deleted_at = 0;

            // Insert the order
            self.orders.insert(&order._id, &order);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single order by ID
    pub fn get_order(&self, id: String) -> anyhow::Result<String> {
        let order = self.orders.get(&id);
        match order {
            Some(order) => Ok(serde_json::to_string(&order)?),
            None => Err(anyhow::anyhow!("not found")),
        }
    }

    /// Update an existing order from JSON string
    pub fn update_order(&mut self, order_json: String) -> anyhow::Result<()> {
        let mut order: Order = serde_json::from_str(&order_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

        // Validate required fields
        if order._id.is_empty() {
            return Err(anyhow::anyhow!("Order ID cannot be empty"));
        }

        if !self.orders.contains(&order._id) {
            return Err(anyhow::anyhow!("Order not found"));
        }

        // Apply timestamp handling logic for updates
        if order.updated_at == 0 {
            order.updated_at = self.get_current_timestamp();
        }

        self.orders.insert(&order._id, &order);
        Ok(())
    }

    /// Apply timestamp handling logic for orders
    pub(crate) fn apply_order_timestamp_handling(&self, order: &mut Order) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if order.created_at == 0 {
            order.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if order.updated_at == 0 {
            order.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Internal implementation for batch updating orders with partial updates
    pub(crate) fn update_many_order(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let update_params: OrderUpdate = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderUpdate JSON: {}", e))?;

        let params = update_params.filter;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find orders matching the filter criteria
        let mut orders_to_update = Vec::new();
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order) = self.orders.get(id) {
                    if self.matches_order_filters(&order, &params) {
                        orders_to_update.push(order);
                    }
                }
            }
        } else {
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order: {}", e))?;
                if self.matches_order_filters(&order, &params) {
                    orders_to_update.push(order);
                }
            }
        }
        // Update each matching service with partial data
        for mut order in orders_to_update {
            // Parse update data as a generic JSON object for partial updates
            if let Some(update_obj) = update_params.update_data.as_object() {
                // Update only specified fields
                for (key, value) in update_obj {
                    match key.as_str() {
                        "orderType" => {
                            if let Some(order_type) = value.as_str() {
                                order.order_type = order_type.to_string();
                            }
                        }
                        "amount" => {
                            if let Some(amount) = value.as_f64() {
                                order.amount = amount;
                            }
                        }

                        "amountPaid" => {
                            if let Some(amount_paid) = value.as_f64() {
                                order.amount_paid = amount_paid;
                            }
                        }
                        "provider" => {
                            if let Some(provider) = value.as_str() {
                                order.provider = provider.to_string();
                            }
                        }
                        "address" => {
                            if let Some(address) = value.as_str() {
                                order.address = address.to_string();
                            }
                        }
                        "recipient" => {
                            if let Some(recipient) = value.as_str() {
                                order.recipient = recipient.to_string();
                            }
                        }
                        "status" => {
                            if let Some(status) = value.as_str() {
                                order.status = status.to_string();
                            }
                        }
                        "lastPaymentTS" => {
                            if let Some(last_payment_ts) = value.as_i64() {
                                order.last_payment_ts = last_payment_ts;
                            }
                        }
                        "paidTS" => {
                            if let Some(paid_ts) = value.as_i64() {
                                order.paid_ts = paid_ts;
                            }
                        }
                        "filedTS" => {
                            if let Some(filed_ts) = value.as_i64() {
                                order.filed_ts = filed_ts;
                            }
                        }
                        "updatedAt" => {
                            if let Some(updated_at) = value.as_i64() {
                                order.updated_at = updated_at;
                            }
                        }
                        "deletedAt" => {
                            if let Some(deleted_at) = value.as_i64() {
                                order.deleted_at = deleted_at;
                            }
                        }
                        _ => {} // Ignore unknown fields
                    }
                }
            }

            self.orders.insert(&order._id, &order);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for bulk write operations on orders
    pub(crate) fn bulk_write_order(&mut self, bulk_write_json_string: String) -> anyhow::Result<String> {
        let operations: Vec<OrderBulkWriteOperation> = serde_json::from_str(&bulk_write_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderBulkWriteOperation JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<Order>(data) {
                            Ok(mut order) => {
                                if order._id.is_empty() {
                                    result.errors.push("Order ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.orders.contains(&order._id) {
                                    result.errors.push(format!("Order with ID '{}' already exists", order._id));
                                    continue;
                                }
                                self.apply_order_timestamp_handling(&mut order);
                                order.deleted_at = 0;
                                self.orders.insert(&order._id, &order);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse order data: {}", e));
                            }
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        let update_params = OrderUpdate {
                            filter,
                            update_data: data,
                        };
                        let update_json = serde_json::to_string(&update_params)?;
                        match self.update_many_order(update_json) {
                            Ok(update_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                    result.updated += batch_result.updated;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Update operation failed: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        let filter_json = serde_json::to_string(&filter)?;
                        match self.delete_many_order(filter_json) {
                            Ok(delete_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                                    result.deleted += batch_result.deleted;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Delete operation failed: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single order (HARD DELETE)
    pub(crate) fn delete_order(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderQueryParam = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderQueryParam JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.orders.contains(id) {
                    // Hard delete: completely remove from storage
                    self.orders.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Otherwise, find the first order matching the filter and delete it
        let mut iter = self.orders.index("order_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let order = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order: {}", e))?;
            if self.matches_order_filters(&order, &params) {
                // Hard delete: completely remove from storage
                self.orders.remove(&order._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting orders
    pub(crate) fn delete_many_order(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderQueryParam = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderQueryParam JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find orders matching the filter criteria
        let mut orders_to_delete = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order) = self.orders.get(id) {
                    if self.matches_order_filters(&order, &params) {
                        orders_to_delete.push(order);
                    }
                }
            }
        } else {
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order: {}", e))?;
                if self.matches_order_filters(&order, &params) {
                    orders_to_delete.push(order);
                }
            }
        }

        // Delete each matching order (HARD DELETE)
        for order in orders_to_delete {
            // Hard delete: completely remove from storage
            self.orders.remove(&order._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Query orders with filtering
    pub fn find_order(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderQueryParam = serde_json::from_str(&params_json)?;

        let mut orders = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX); // Default limit
        let offset = params.offset.unwrap_or(0);

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order) = self.orders.get(id) {
                    if self.matches_order_filters(&order, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if orders.len() >= limit as usize {
                            break;
                        }
                        orders.push(order);
                        count += 1;
                    }
                }
            }
        } else if let (Some(ref address), Some(ref statuses)) = (&params.address, &params.statuses) {
            // Use composite address_status index
            for status in statuses {
                let key_prefix = format!("{}-{}", address, status);
                self.query_orders_with_index("order_address_status", &key_prefix, &params, &mut orders, &mut count, limit, offset)?;
                if orders.len() >= limit as usize {
                    break;
                }
            }
        } else if let Some(ref address) = params.address {
            // Use address index
            self.query_orders_with_index("order_address", address, &params, &mut orders, &mut count, limit, offset)?;
        } else if let Some(ref recipient) = params.recipient {
            // Use recipient index
            self.query_orders_with_index("order_recipient", recipient, &params, &mut orders, &mut count, limit, offset)?;
        } else if let Some(ref service) = params.service {
            // Use provider index (service maps to provider)
            self.query_orders_with_index("order_provider", service, &params, &mut orders, &mut count, limit, offset)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index
            self.query_orders_with_index("order_type", order_type, &params, &mut orders, &mut count, limit, offset)?;
        } else if let Some(ref statuses) = params.statuses {
            // Use status index
            for status in statuses {
                self.query_orders_with_index("order_status", status, &params, &mut orders, &mut count, limit, offset)?;
                if orders.len() >= limit as usize {
                    break;
                }
            }
        } else {
            // Use created_at index
            self.query_with_order_created_at_index(&params, &mut orders, &mut count, limit, offset)?;
        }

        Ok(serde_json::to_string(&orders)?)
    }

    /// Count orders with advanced filtering
    pub fn count_order(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderQueryParam = serde_json::from_str(&params_json)?;
        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        if let Some(ref ids) = params.ids {
            // Batch ID query - count orders by IDs directly
            for id in ids {
                if let Some(order) = self.orders.get(id) {
                    if self.matches_order_filters(&order, &params) {
                        count += 1;
                    }
                }
            }
        } else if let (Some(ref address), Some(ref statuses)) = (&params.address, &params.statuses) {
            // Use composite address_status index for efficient counting
            for status in statuses {
                let key_prefix = format!("{}-{}", address, status);
                self.count_with_composite_index("order_address_status", &key_prefix, &params, &mut count)?;
            }
        } else if let Some(ref address) = params.address {
            // Use address index for efficient counting
            self.count_with_composite_index("order_address", address, &params, &mut count)?;
        } else if let Some(ref recipient) = params.recipient {
            // Use recipient index for efficient counting
            self.count_with_composite_index("order_recipient", recipient, &params, &mut count)?;
        } else if let Some(ref service) = params.service {
            // Use provider index for efficient counting (service maps to provider)
            self.count_with_composite_index("order_provider", service, &params, &mut count)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index for efficient counting
            self.count_with_composite_index("order_type", order_type, &params, &mut count)?;
        } else if let Some(ref statuses) = params.statuses {
            // Use status index for efficient counting
            for status in statuses {
                self.count_with_composite_index("order_status", status, &params, &mut count)?;
            }
        } else {
            // No specific index, use created_at index for counting
            self.count_with_created_at_index(&params, &mut count)?;
        }

        Ok(count.to_string())
    }

    /// Helper function to check if an order matches the given filters
    pub(crate) fn matches_order_filters(&self, order: &Order, params: &OrderQueryParam) -> bool {
        // Skip deleted orders unless specifically querying for them
        if order.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&order._id) {
                return false;
            }
        }

        // Check address filter
        if let Some(ref address) = params.address {
            if order.address != *address {
                return false;
            }
        }

        // Check recipient filter
        if let Some(ref recipient) = params.recipient {
            if order.recipient != *recipient {
                return false;
            }
        }

        // Check provider filter (using service field for provider matching)
        if let Some(ref service) = params.service {
            if order.provider != *service {
                return false;
            }
        }

        // Check order_type filter
        if let Some(ref order_type) = params.order_type {
            if order.order_type != *order_type {
                return false;
            }
        }

        // Check statuses filter
        if let Some(ref statuses) = params.statuses {
            if !statuses.contains(&order.status) {
                return false;
            }
        }

        // Check time range filter (TSStart and TSEnd)
        if let (Some(ts_start), Some(ts_end)) = (params.ts_start, params.ts_end) {
            // Filter records where (paidTS OR filedTS) is greater than TSStart AND (paidTS OR filedTS) is less than TSEnd
            let relevant_ts = if order.paid_ts > 0 { order.paid_ts } else { order.filed_ts };
            if relevant_ts <= ts_start || relevant_ts >= ts_end {
                return false;
            }
        }

        true
    }

    /// Helper function for querying orders with composite index and sorting
    pub(crate) fn query_orders_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &OrderQueryParam,
        orders: &mut Vec<Order>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first
            (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
        } else {
            // Ascending: oldest first
            (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
        };

        let mut iter = self.orders.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let order = iter.value()?;
            if self.matches_order_filters(&order, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if orders.len() >= limit as usize {
                    break;
                }
                orders.push(order);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Helper function for querying orders with created_at index and sorting
    pub(crate) fn query_with_order_created_at_index(
        &self,
        params: &OrderQueryParam,
        orders: &mut Vec<Order>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.orders.index("order_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let order = iter.value()?;
            if self.matches_order_filters(&order, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if orders.len() >= limit as usize {
                    break;
                }
                orders.push(order);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Helper function for counting orders using composite indexes
    pub(crate) fn count_with_composite_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &OrderQueryParam,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        let (start_key, end_key) = (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.orders.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let order = iter.value()?;
            if self.matches_order_filters(&order, params) {
                *count += 1;
            }
        }

        Ok(())
    }

    /// Helper function for counting orders using created_at index
    pub(crate) fn count_with_created_at_index(
        &self,
        params: &OrderQueryParam,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        let mut iter = self.orders.index("order_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let order = iter.value()?;
            if self.matches_order_filters(&order, params) {
                *count += 1;
            }
        }

        Ok(())
    }
}


