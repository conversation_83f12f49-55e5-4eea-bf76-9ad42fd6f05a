#[cfg(test)]
mod currency_tests {
    use super::*;

    fn create_test_currency(id: &str) -> Currency {
        Currency {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            name_or_id: "Bitcoin".to_string(),
            contract_id: "0x123456789".to_string(),
            symbol_name: "BTC".to_string(),
            contract_type: "ERC20".to_string(),
            unit: 8,
            exchange_rate: 45000.0,
        }
    }

    #[glue::test]
    fn test_insert_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("test_currency_1");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        let result = db.insert_currency(currency_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_currency_1");
        
        // Verify currency was inserted
        assert!(db.currencies.contains(&"test_currency_1".to_string()));
    }

    #[glue::test]
    fn test_get_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("get_test_1");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency first
        db.insert_currency(currency_json).unwrap();
        
        // Get currency
        let result = db.get_currency("get_test_1".to_string());
        assert!(result.is_ok());
        
        let retrieved_currency: Currency = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_currency._id, "get_test_1");
        assert_eq!(retrieved_currency.symbol_name, "BTC");
    }

    #[glue::test]
    fn test_count_currency() {
        let mut db = VCloudDB::new();
        let currency1 = create_test_currency("count_test_1");
        let currency2 = create_test_currency("count_test_2");
        
        // Insert currencies
        db.insert_currency(serde_json::to_string(&currency1).unwrap()).unwrap();
        db.insert_currency(serde_json::to_string(&currency2).unwrap()).unwrap();
        
        // Count all currencies
        let query_params = CurrencyQueryParams {
            ids: None,
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.count_currency(serde_json::to_string(&query_params).unwrap());
        assert!(result.is_ok());
        
        let count: u64 = result.unwrap().parse().unwrap();
        assert!(count >= 2);
    }
}
