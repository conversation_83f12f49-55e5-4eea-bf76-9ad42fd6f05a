#[cfg(test)]
mod service_category_tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_service_category(id: &str) -> ServiceCategory {
        let mut service_options = HashMap::new();
        service_options.insert("cpu".to_string(), vec!["1".to_string(), "2".to_string(), "4".to_string()]);
        service_options.insert("memory".to_string(), vec!["1GB".to_string(), "2GB".to_string(), "4GB".to_string()]);
        
        let mut name2_id = HashMap::new();
        name2_id.insert("compute".to_string(), "compute_001".to_string());
        name2_id.insert("storage".to_string(), "storage_001".to_string());

        ServiceCategory {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            provider: "test_provider".to_string(),
            name: "Compute Services".to_string(),
            service_options,
            description: "Test compute services category".to_string(),
            name2_id,
            api_host: "api.test.com".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_service_category() {
        let mut db = VCloudDB::new();
        let category = create_test_service_category("test_category_1");
        let category_json = serde_json::to_string(&category).unwrap();
        
        let result = db.insert_service_category(category_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_category_1");
        
        // Verify category was inserted
        assert!(db.service_categories.contains(&"test_category_1".to_string()));
    }

    #[glue::test]
    fn test_get_service_category() {
        let mut db = VCloudDB::new();
        let category = create_test_service_category("test_category_2");
        let category_json = serde_json::to_string(&category).unwrap();
        
        // Insert category first
        db.insert_service_category(category_json).unwrap();
        
        // Get category
        let result = db.get_service_category("test_category_2".to_string());
        assert!(result.is_ok());
        
        let retrieved_category: ServiceCategory = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_category._id, "test_category_2");
        assert_eq!(retrieved_category.provider, "test_provider");
        assert_eq!(retrieved_category.name, "Compute Services");
    }

    #[glue::test]
    fn test_insert_many_service_category() {
        let mut db = VCloudDB::new();
        let categories = vec![
            create_test_service_category("batch_1"),
            create_test_service_category("batch_2"),
        ];
        let categories_json = serde_json::to_string(&categories).unwrap();
        
        let result = db.insert_many_service_category(categories_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_find_service_category() {
        let mut db = VCloudDB::new();
        let category1 = create_test_service_category("find_test_1");
        let mut category2 = create_test_service_category("find_test_2");
        category2.provider = "different_provider".to_string();
        
        // Insert categories
        db.insert_service_category(serde_json::to_string(&category1).unwrap()).unwrap();
        db.insert_service_category(serde_json::to_string(&category2).unwrap()).unwrap();
        
        // Query by provider
        let query_params = ServiceCategoryQueryParams {
            ids: None,
            provider: Some("test_provider".to_string()),
            name: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            names: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_service_category(query_json);
        assert!(result.is_ok());
        
        let categories: Vec<ServiceCategory> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(categories.len(), 1);
        assert_eq!(categories[0]._id, "find_test_1");
    }

    #[glue::test]
    fn test_count_service_category() {
        let mut db = VCloudDB::new();
        let category1 = create_test_service_category("count_test_1");
        let category2 = create_test_service_category("count_test_2");
        
        // Insert categories
        db.insert_service_category(serde_json::to_string(&category1).unwrap()).unwrap();
        db.insert_service_category(serde_json::to_string(&category2).unwrap()).unwrap();
        
        // Count all categories
        let query_params = ServiceCategoryQueryParams {
            ids: None,
            provider: None,
            name: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            names: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_service_category(query_json);
        assert!(result.is_ok());
        
        let count: u64 = result.unwrap().parse().unwrap();
        assert!(count >= 2);
    }

    #[glue::test]
    fn test_update_service_category() {
        let mut db = VCloudDB::new();
        let mut category = create_test_service_category("update_test");
        let category_json = serde_json::to_string(&category).unwrap();
        
        // Insert category first
        db.insert_service_category(category_json).unwrap();
        
        // Update category
        category.description = "Updated description".to_string();
        category.api_host = "updated.api.com".to_string();
        let updated_json = serde_json::to_string(&category).unwrap();
        
        let result = db.update_service_category(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_service_category("update_test".to_string()).unwrap();
        let retrieved_category: ServiceCategory = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_category.description, "Updated description");
        assert_eq!(retrieved_category.api_host, "updated.api.com");
    }

    #[glue::test]
    fn test_delete_service_category() {
        let mut db = VCloudDB::new();
        let category = create_test_service_category("delete_test");
        let category_json = serde_json::to_string(&category).unwrap();
        
        // Insert category first
        db.insert_service_category(category_json).unwrap();
        
        // Delete category
        let filter_params = ServiceCategoryQueryParams {
            ids: Some(vec!["delete_test".to_string()]),
            provider: None,
            name: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            names: None,
        };
        let filter_json = serde_json::to_string(&filter_params).unwrap();
        
        let result = db.delete_service_category(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion
        assert!(!db.service_categories.contains(&"delete_test".to_string()));
    }

    #[glue::test]
    fn test_delete_many_service_category() {
        let mut db = VCloudDB::new();
        let category1 = create_test_service_category("delete_many_1");
        let category2 = create_test_service_category("delete_many_2");
        
        // Insert categories
        db.insert_service_category(serde_json::to_string(&category1).unwrap()).unwrap();
        db.insert_service_category(serde_json::to_string(&category2).unwrap()).unwrap();
        
        // Delete by provider
        let filter_params = ServiceCategoryQueryParams {
            ids: None,
            provider: Some("test_provider".to_string()),
            name: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            names: None,
        };
        let filter_json = serde_json::to_string(&filter_params).unwrap();
        
        let result = db.delete_many_service_category(filter_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
    }

    #[glue::test]
    fn test_bulk_write_service_category() {
        let mut db = VCloudDB::new();
        
        // Create bulk write operations
        let operations = vec![
            ServiceCategoryBulkWriteOperation {
                operation_type: "insert".to_string(),
                filter: None,
                data: Some(serde_json::to_value(create_test_service_category("bulk_1")).unwrap()),
            },
            ServiceCategoryBulkWriteOperation {
                operation_type: "insert".to_string(),
                filter: None,
                data: Some(serde_json::to_value(create_test_service_category("bulk_2")).unwrap()),
            },
        ];
        let operations_json = serde_json::to_string(&operations).unwrap();
        
        let result = db.bulk_write_service_category(operations_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_service_category_error_handling() {
        let mut db = VCloudDB::new();
        
        // Test empty ID
        let mut category = create_test_service_category("");
        category._id = "".to_string();
        let category_json = serde_json::to_string(&category).unwrap();
        
        let result = db.insert_service_category(category_json);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("ID cannot be empty"));
        
        // Test duplicate ID
        let category1 = create_test_service_category("duplicate_test");
        let category2 = create_test_service_category("duplicate_test");
        
        db.insert_service_category(serde_json::to_string(&category1).unwrap()).unwrap();
        let result = db.insert_service_category(serde_json::to_string(&category2).unwrap());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("already exists"));
        
        // Test get non-existent category
        let result = db.get_service_category("non_existent".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }
}
