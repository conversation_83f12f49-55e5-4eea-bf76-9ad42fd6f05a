#[cfg(test)]
mod provider_tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_provider(id: &str) -> Provider {
        let mut category2_id = HashMap::new();
        category2_id.insert("compute".to_string(), "comp_001".to_string());
        category2_id.insert("storage".to_string(), "stor_001".to_string());

        Provider {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            name: "test_provider".to_string(),
            wallet_address: "0xtest_wallet_address".to_string(),
            publickey: "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d".to_string(),
            category2_id,
            sign_address: "0xtest_sign_address".to_string(),
            api_host: "https://api.test-provider.com".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_provider() {
        let mut db = VCloudDB::new();
        let provider = create_test_provider("test_provider_1");
        let provider_json = serde_json::to_string(&provider).unwrap();
        
        let result = db.insert_provider(provider_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_provider_1");
        
        // Verify the provider was inserted
        let retrieved = db.get_provider("test_provider_1".to_string()).unwrap();
        let retrieved_provider: Provider = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_provider._id, "test_provider_1");
        assert_eq!(retrieved_provider.name, "test_provider");
    }

    #[glue::test]
    fn test_insert_provider_duplicate_id() {
        let mut db = VCloudDB::new();
        let provider = create_test_provider("duplicate_test");
        let provider_json = serde_json::to_string(&provider).unwrap();
        
        // Insert first time should succeed
        let result1 = db.insert_provider(provider_json.clone());
        assert!(result1.is_ok());
        
        // Insert second time should fail
        let result2 = db.insert_provider(provider_json);
        assert!(result2.is_err());
        assert!(result2.unwrap_err().to_string().contains("already exists"));
    }

    #[glue::test]
    fn test_insert_many_provider() {
        let mut db = VCloudDB::new();
        let provider1 = create_test_provider("batch_test_1");
        let provider2 = create_test_provider("batch_test_2");
        let providers = vec![provider1, provider2];
        let providers_json = serde_json::to_string(&providers).unwrap();
        
        let result = db.insert_many_provider(providers_json).unwrap();
        let batch_result: BatchResult = serde_json::from_str(&result).unwrap();
        
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify both providers were inserted
        assert!(db.get_provider("batch_test_1".to_string()).is_ok());
        assert!(db.get_provider("batch_test_2".to_string()).is_ok());
    }

    #[glue::test]
    fn test_get_provider() {
        let mut db = VCloudDB::new();
        let provider = create_test_provider("get_test_1");
        let provider_json = serde_json::to_string(&provider).unwrap();
        
        // Insert provider first
        db.insert_provider(provider_json).unwrap();
        
        // Get provider
        let result = db.get_provider("get_test_1".to_string());
        assert!(result.is_ok());
        
        let retrieved_provider: Provider = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_provider._id, "get_test_1");
        assert_eq!(retrieved_provider.name, "test_provider");
    }

    #[glue::test]
    fn test_get_provider_not_found() {
        let db = VCloudDB::new();
        let result = db.get_provider("nonexistent".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }

    #[glue::test]
    fn test_update_provider() {
        let mut db = VCloudDB::new();
        let mut provider = create_test_provider("update_test_1");
        let provider_json = serde_json::to_string(&provider).unwrap();
        
        // Insert provider first
        db.insert_provider(provider_json).unwrap();
        
        // Update provider
        provider.name = "updated_provider".to_string();
        provider.api_host = "https://updated-api.test-provider.com".to_string();
        let updated_json = serde_json::to_string(&provider).unwrap();
        
        let result = db.update_provider(updated_json);
        assert!(result.is_ok());
        
        // Verify the update
        let retrieved = db.get_provider("update_test_1".to_string()).unwrap();
        let retrieved_provider: Provider = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_provider.name, "updated_provider");
        assert_eq!(retrieved_provider.api_host, "https://updated-api.test-provider.com");
    }

    #[glue::test]
    fn test_find_provider() {
        let mut db = VCloudDB::new();
        let provider1 = create_test_provider("find_test_1");
        let provider2 = create_test_provider("find_test_2");
        
        // Insert providers
        db.insert_provider(serde_json::to_string(&provider1).unwrap()).unwrap();
        db.insert_provider(serde_json::to_string(&provider2).unwrap()).unwrap();
        
        // Find all providers
        let query_params = ProviderQueryParams {
            ids: None,
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.find_provider(serde_json::to_string(&query_params).unwrap()).unwrap();
        let providers: Vec<Provider> = serde_json::from_str(&result).unwrap();
        
        assert!(providers.len() >= 2);
        let ids: Vec<String> = providers.iter().map(|p| p._id.clone()).collect();
        assert!(ids.contains(&"find_test_1".to_string()));
        assert!(ids.contains(&"find_test_2".to_string()));
    }

    #[glue::test]
    fn test_find_provider_by_name() {
        let mut db = VCloudDB::new();
        let mut provider1 = create_test_provider("name_test_1");
        provider1.name = "unique_provider_name".to_string();
        let provider2 = create_test_provider("name_test_2");
        
        // Insert providers
        db.insert_provider(serde_json::to_string(&provider1).unwrap()).unwrap();
        db.insert_provider(serde_json::to_string(&provider2).unwrap()).unwrap();
        
        // Find by specific name
        let query_params = ProviderQueryParams {
            ids: None,
            name: Some("unique_provider_name".to_string()),
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.find_provider(serde_json::to_string(&query_params).unwrap()).unwrap();
        let providers: Vec<Provider> = serde_json::from_str(&result).unwrap();
        
        assert_eq!(providers.len(), 1);
        assert_eq!(providers[0].name, "unique_provider_name");
    }

    #[glue::test]
    fn test_count_provider() {
        let mut db = VCloudDB::new();
        let provider1 = create_test_provider("count_test_1");
        let provider2 = create_test_provider("count_test_2");
        
        // Insert providers
        db.insert_provider(serde_json::to_string(&provider1).unwrap()).unwrap();
        db.insert_provider(serde_json::to_string(&provider2).unwrap()).unwrap();
        
        // Count all providers
        let query_params = ProviderQueryParams {
            ids: None,
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.count_provider(serde_json::to_string(&query_params).unwrap());
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "2");
    }

    #[glue::test]
    fn test_delete_provider() {
        let mut db = VCloudDB::new();
        let provider = create_test_provider("delete_test_1");
        let provider_json = serde_json::to_string(&provider).unwrap();
        
        // Insert provider first
        db.insert_provider(provider_json).unwrap();
        
        // Delete provider
        let filter = ProviderQueryParams {
            ids: Some(vec!["delete_test_1".to_string()]),
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.delete_provider(serde_json::to_string(&filter).unwrap()).unwrap();
        let delete_result: serde_json::Value = serde_json::from_str(&result).unwrap();
        assert_eq!(delete_result["deleted"].as_u64().unwrap(), 1);
        
        // Verify provider was deleted
        let get_result = db.get_provider("delete_test_1".to_string());
        assert!(get_result.is_err());
    }

    #[glue::test]
    fn test_delete_many_provider() {
        let mut db = VCloudDB::new();
        let provider1 = create_test_provider("delete_many_test_1");
        let provider2 = create_test_provider("delete_many_test_2");
        
        // Insert providers
        db.insert_provider(serde_json::to_string(&provider1).unwrap()).unwrap();
        db.insert_provider(serde_json::to_string(&provider2).unwrap()).unwrap();
        
        // Delete multiple providers
        let filter = ProviderQueryParams {
            ids: Some(vec!["delete_many_test_1".to_string(), "delete_many_test_2".to_string()]),
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let result = db.delete_many_provider(serde_json::to_string(&filter).unwrap()).unwrap();
        let batch_result: BatchResult = serde_json::from_str(&result).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify providers were deleted
        assert!(db.get_provider("delete_many_test_1".to_string()).is_err());
        assert!(db.get_provider("delete_many_test_2".to_string()).is_err());
    }

    #[glue::test]
    fn test_update_many_provider() {
        let mut db = VCloudDB::new();
        let provider1 = create_test_provider("update_many_test_1");
        let provider2 = create_test_provider("update_many_test_2");

        // Insert providers
        db.insert_provider(serde_json::to_string(&provider1).unwrap()).unwrap();
        db.insert_provider(serde_json::to_string(&provider2).unwrap()).unwrap();

        // Update multiple providers
        let filter = ProviderQueryParams {
            ids: Some(vec!["update_many_test_1".to_string(), "update_many_test_2".to_string()]),
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };

        let mut update_data = serde_json::Map::new();
        update_data.insert("name".to_string(), serde_json::Value::String("updated_provider_name".to_string()));
        update_data.insert("apiHost".to_string(), serde_json::Value::String("https://updated-api.com".to_string()));

        let update_params = ProviderUpdate {
            filter,
            update_data: serde_json::Value::Object(update_data),
        };

        let result = db.update_many_provider(serde_json::to_string(&update_params).unwrap()).unwrap();
        let batch_result: BatchResult = serde_json::from_str(&result).unwrap();
        assert_eq!(batch_result.updated, 2);

        // Verify updates
        let retrieved1 = db.get_provider("update_many_test_1".to_string()).unwrap();
        let provider1: Provider = serde_json::from_str(&retrieved1).unwrap();
        assert_eq!(provider1.name, "updated_provider_name");
        assert_eq!(provider1.api_host, "https://updated-api.com");

        let retrieved2 = db.get_provider("update_many_test_2".to_string()).unwrap();
        let provider2: Provider = serde_json::from_str(&retrieved2).unwrap();
        assert_eq!(provider2.name, "updated_provider_name");
        assert_eq!(provider2.api_host, "https://updated-api.com");
    }

    #[glue::test]
    fn test_bulk_write_provider() {
        let mut db = VCloudDB::new();

        // Create bulk write operations
        let provider1 = create_test_provider("bulk_test_1");
        let provider2 = create_test_provider("bulk_test_2");

        let insert_op1 = ProviderBulkWriteOperation {
            operation_type: "insert".to_string(),
            filter: None,
            data: Some(serde_json::to_value(&provider1).unwrap()),
        };

        let insert_op2 = ProviderBulkWriteOperation {
            operation_type: "insert".to_string(),
            filter: None,
            data: Some(serde_json::to_value(&provider2).unwrap()),
        };

        let operations = vec![insert_op1, insert_op2];

        let result = db.bulk_write_provider(serde_json::to_string(&operations).unwrap()).unwrap();
        let batch_result: BatchResult = serde_json::from_str(&result).unwrap();

        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);

        // Verify providers were created
        assert!(db.get_provider("bulk_test_1".to_string()).is_ok());
        assert!(db.get_provider("bulk_test_2".to_string()).is_ok());

        // Test delete_many operation
        let delete_filter = ProviderQueryParams {
            ids: Some(vec!["bulk_test_1".to_string(), "bulk_test_2".to_string()]),
            name: None,
            wallet_address: None,
            publickey: None,
            sign_address: None,
            api_host: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };

        let delete_op = ProviderBulkWriteOperation {
            operation_type: "delete_many".to_string(),
            filter: Some(delete_filter),
            data: None,
        };

        let delete_operations = vec![delete_op];
        let delete_result = db.bulk_write_provider(serde_json::to_string(&delete_operations).unwrap()).unwrap();
        let delete_batch_result: BatchResult = serde_json::from_str(&delete_result).unwrap();

        assert_eq!(delete_batch_result.deleted, 2);

        // Verify providers were deleted
        assert!(db.get_provider("bulk_test_1".to_string()).is_err());
        assert!(db.get_provider("bulk_test_2".to_string()).is_err());
    }
}
