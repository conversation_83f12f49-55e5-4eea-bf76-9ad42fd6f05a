#[cfg(test)]
mod order_service_tests {
    use super::*;

    fn create_test_order_service(id: &str) -> OrderService {
        OrderService {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            order_id: "order_001".to_string(),
            user_service_id: "user_service_001".to_string(),
            order_status: "pending".to_string(),
            order_type: "compute".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("test_order_service_1");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        let result = db.insert_order_service(order_service_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_order_service_1");
        
        // Verify order service was inserted
        assert!(db.order_services.contains(&"test_order_service_1".to_string()));
    }

    #[glue::test]
    fn test_get_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("test_order_service_2");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service first
        db.insert_order_service(order_service_json).unwrap();
        
        // Get order service
        let result = db.get_order_service("test_order_service_2".to_string());
        assert!(result.is_ok());
        
        let retrieved_order_service: OrderService = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_order_service._id, "test_order_service_2");
        assert_eq!(retrieved_order_service.order_id, "order_001");
        assert_eq!(retrieved_order_service.user_service_id, "user_service_001");
    }

    #[glue::test]
    fn test_insert_many_order_service() {
        let mut db = VCloudDB::new();
        let order_services = vec![
            create_test_order_service("batch_1"),
            create_test_order_service("batch_2"),
        ];
        let order_services_json = serde_json::to_string(&order_services).unwrap();
        
        let result = db.insert_many_order_service(order_services_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_find_order_service() {
        let mut db = VCloudDB::new();
        let order_service1 = create_test_order_service("find_test_1");
        let mut order_service2 = create_test_order_service("find_test_2");
        order_service2.order_type = "storage".to_string();
        
        // Insert order services
        db.insert_order_service(serde_json::to_string(&order_service1).unwrap()).unwrap();
        db.insert_order_service(serde_json::to_string(&order_service2).unwrap()).unwrap();
        
        // Query by order_type
        let query_params = OrderServiceQueryParams {
            ids: None,
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: Some("compute".to_string()),
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            user_service_ids: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_order_service(query_json);
        assert!(result.is_ok());
        
        let found_order_services: Vec<OrderService> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_order_services.len(), 1);
        assert_eq!(found_order_services[0]._id, "find_test_1");
    }

    #[glue::test]
    fn test_count_order_service() {
        let mut db = VCloudDB::new();
        let order_service1 = create_test_order_service("count_test_1");
        let order_service2 = create_test_order_service("count_test_2");
        
        // Insert order services
        db.insert_order_service(serde_json::to_string(&order_service1).unwrap()).unwrap();
        db.insert_order_service(serde_json::to_string(&order_service2).unwrap()).unwrap();
        
        // Count all order services
        let query_params = OrderServiceQueryParams {
            ids: None,
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            user_service_ids: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_order_service(query_json);
        assert!(result.is_ok());
        
        let count: u64 = result.unwrap().parse().unwrap();
        assert_eq!(count, 2);
    }

    #[glue::test]
    fn test_update_order_service() {
        let mut db = VCloudDB::new();
        let mut order_service = create_test_order_service("update_test");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service first
        db.insert_order_service(order_service_json).unwrap();
        
        // Update order service
        order_service.order_status = "completed".to_string();
        order_service.order_type = "storage".to_string();
        let updated_order_service_json = serde_json::to_string(&order_service).unwrap();
        
        let result = db.update_order_service(updated_order_service_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_order_service("update_test".to_string()).unwrap();
        let retrieved_order_service: OrderService = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_order_service.order_status, "completed");
        assert_eq!(retrieved_order_service.order_type, "storage");
    }

    #[glue::test]
    fn test_delete_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("delete_test");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service first
        db.insert_order_service(order_service_json).unwrap();
        
        // Delete order service
        let delete_params = OrderServiceQueryParams {
            ids: Some(vec!["delete_test".to_string()]),
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            user_service_ids: None,
        };
        let delete_json = serde_json::to_string(&delete_params).unwrap();
        
        let result = db.delete_order_service(delete_json);
        assert!(result.is_ok());
        
        // Verify deletion
        assert!(!db.order_services.contains(&"delete_test".to_string()));
    }

    #[glue::test]
    fn test_delete_many_order_service() {
        let mut db = VCloudDB::new();
        let order_service1 = create_test_order_service("delete_many_1");
        let order_service2 = create_test_order_service("delete_many_2");
        
        // Insert order services
        db.insert_order_service(serde_json::to_string(&order_service1).unwrap()).unwrap();
        db.insert_order_service(serde_json::to_string(&order_service2).unwrap()).unwrap();
        
        // Delete by order_type
        let delete_params = OrderServiceQueryParams {
            ids: None,
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: Some("compute".to_string()),
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            user_service_ids: None,
        };
        let delete_json = serde_json::to_string(&delete_params).unwrap();
        
        let result = db.delete_many_order_service(delete_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify deletions
        assert!(!db.order_services.contains(&"delete_many_1".to_string()));
        assert!(!db.order_services.contains(&"delete_many_2".to_string()));
    }

    #[glue::test]
    fn test_order_service_validation() {
        let mut db = VCloudDB::new();
        
        // Test empty ID validation
        let mut order_service = create_test_order_service("");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        let result = db.insert_order_service(order_service_json);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("ID cannot be empty"));
        
        // Test duplicate ID validation
        order_service._id = "duplicate_test".to_string();
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        db.insert_order_service(order_service_json.clone()).unwrap();
        let result = db.insert_order_service(order_service_json);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("already exists"));
    }

    #[glue::test]
    fn test_order_service_timestamp_handling() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("timestamp_test");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service
        db.insert_order_service(order_service_json).unwrap();
        
        // Get order service and check timestamps
        let retrieved = db.get_order_service("timestamp_test".to_string()).unwrap();
        let retrieved_order_service: OrderService = serde_json::from_str(&retrieved).unwrap();
        
        // Timestamps should be set automatically
        assert!(retrieved_order_service.created_at > 0);
        assert!(retrieved_order_service.updated_at > 0);
        assert_eq!(retrieved_order_service.deleted_at, 0);
    }
}
