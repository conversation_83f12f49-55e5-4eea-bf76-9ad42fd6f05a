{"rustc": 9935547201935341413, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 14790166133050072309, "profile": 385020235239010606, "path": 10745672887196906641, "deps": [[7006636483571730090, "unicode_ident", false, 13995188779157752333], [17525013869477438691, "quote", false, 9005921310000998163], [18036439996138669183, "proc_macro2", false, 17017809367127854165]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-ccd33baae1aa10e3/dep-lib-syn", "checksum": false}}], "rustflags": [], "metadata": 6886477143387768027, "config": 2202906307356721367, "compile_kind": 0}