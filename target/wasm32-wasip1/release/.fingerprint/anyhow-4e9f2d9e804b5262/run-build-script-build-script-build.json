{"rustc": 9935547201935341413, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10291739091677281249, "build_script_build", false, 3574170315854787643]], "local": [{"RerunIfChanged": {"output": "wasm32-wasip1/release/build/anyhow-4e9f2d9e804b5262/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}